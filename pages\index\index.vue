<template>
	<view class="search-container">
		<!-- 欢迎区域 -->
		<view class="welcome-section">
			<view class="menu-icon">
				<image class="menu-line" src="/static/figma-icons/menu-line1.svg" mode="aspectFit"></image>
				<image class="menu-line" src="/static/figma-icons/menu-line2.svg" mode="aspectFit"></image>
				<image class="menu-line" src="/static/figma-icons/menu-line3.svg" mode="aspectFit"></image>
			</view>
			<view class="welcome-text">
				<text class="greeting">👋 Hello, Murad</text>
				<text class="subtitle">Welcome to the app</text>
			</view>
			<view class="search-icon">
				<image class="search-img" src="/static/figma-icons/search-icon.svg" mode="aspectFit"></image>
			</view>
		</view>

		<!-- 主要内容区域 -->
		<view class="main-content">
			<!-- 搜索栏 -->
			<view class="search-bar">
				<view class="search-input">
					<image class="search-icon-small" src="/static/figma-icons/search-nav-icon.svg" mode="aspectFit"></image>
					<input class="search-input-field" placeholder="Search for a test" placeholder-style="color: #9D9D9D;" />
				</view>
				<view class="filter-icon">
					<image class="filter-img" src="/static/figma-icons/filter-icon-black.svg" mode="aspectFit"></image>
				</view>
			</view>

			<!-- Live Chat 组件 -->
			<view class="live-chat-section">
				<view class="chat-card">
					<view class="chat-progress">
						<text class="progress-text">1 / 4</text>
					</view>
					<view class="chat-content">
						<text class="chat-title">Live Chat</text>
						<text class="chat-description">If you have any questions about the application or the tests, please contact us.</text>
					</view>
					<image class="chat-image" src="/static/figma-icons/chat-bot.png" mode="aspectFit"></image>
				</view>
			</view>

			<!-- Categories 标题 -->
			<view class="categories-header">
				<text class="categories-title">Categories</text>
				<view class="categories-filter">
					<text class="filter-text">All</text>
				</view>
			</view>

			<!-- 分类卡片 -->
			<view class="categories-grid">
				<view class="category-row">
					<view class="category-card featured" @click="goToCategory('applicant')">
						<view class="card-content">
							<view class="card-left">
								<view class="card-icon-container">
									<image class="category-icon" src="/static/figma-icons/applicant-icon.svg" mode="aspectFit"></image>
								</view>
								<view class="card-text">
									<text class="card-title">Applicant</text>
									<text class="see-more">See more</text>
								</view>
							</view>
							<view class="arrow-icon">
								<image class="arrow-img" src="/static/figma-icons/arrow-right.svg" mode="aspectFit"></image>
							</view>
						</view>
					</view>

					<view class="category-card featured" @click="goToCategory('master')">
						<view class="card-content">
							<view class="card-left">
								<view class="card-icon-container">
									<image class="category-icon" src="/static/figma-icons/master-icon.svg" mode="aspectFit"></image>
								</view>
								<view class="card-text">
									<text class="card-title">Master</text>
									<text class="see-more">See more</text>
								</view>
							</view>
							<view class="arrow-icon">
								<image class="arrow-img" src="/static/figma-icons/arrow-right.svg" mode="aspectFit"></image>
							</view>
						</view>
					</view>
				</view>


			</view>

			<!-- 搜索结果卡片列表 -->
			<view class="search-results-section">
				<view class="result-row">
					<view class="result-card" @click="goToTest('master-informatics')">
						<view class="result-content">
							<view class="result-header">
								<view class="result-meta">
									<image class="calendar-icon" src="/static/figma-icons/calendar-icon.svg" mode="aspectFit"></image>
									<text class="result-date">16.04.2025</text>
								</view>
								<image class="bookmark-icon" src="/static/figma-icons/bookmark-icon.svg" mode="aspectFit"></image>
							</view>
							<view class="result-title-section">
								<text class="result-title">Master of Informatics</text>
							</view>
							<view class="result-author">
								<image class="author-avatar" src="/static/figma-icons/author-avatar.png" mode="aspectFit"></image>
								<text class="author-name">Murad Azimzade</text>
							</view>
							<view class="result-description">
								<text class="more-text">More</text>
								<text class="description-text">There are 4 tests per section
The number of questions is 60
The test duration is 3 hours.</text>
							</view>
						</view>
						<view class="start-button" @click.stop="startTest('master-informatics')">
							<text class="start-text">Start</text>
						</view>
					</view>

					<view class="result-card" @click="goToTest('master-informatics-2')">
						<view class="result-content">
							<view class="result-header">
								<view class="result-meta">
									<image class="calendar-icon" src="/static/figma-icons/calendar-icon.svg" mode="aspectFit"></image>
									<text class="result-date">16.04.2025</text>
								</view>
								<image class="bookmark-icon" src="/static/figma-icons/bookmark-icon.svg" mode="aspectFit"></image>
							</view>
							<view class="result-title-section">
								<text class="result-title">Master of Informatics</text>
							</view>
							<view class="result-author">
								<image class="author-avatar" src="/static/figma-icons/author-avatar.png" mode="aspectFit"></image>
								<text class="author-name">Murad Azimzade</text>
							</view>
							<view class="result-description">
								<text class="more-text">More</text>
								<text class="description-text">There are 4 tests per section
The number of questions is 60
The test duration is 3 hours.</text>
							</view>
						</view>
						<view class="start-button" @click.stop="startTest('master-informatics-2')">
							<text class="start-text">Start</text>
						</view>
					</view>
				</view>

				<view class="result-row">
					<view class="result-card" @click="goToTest('master-informatics-3')">
						<view class="result-content">
							<view class="result-header">
								<view class="result-meta">
									<image class="calendar-icon" src="/static/figma-icons/calendar-icon.svg" mode="aspectFit"></image>
									<text class="result-date">16.04.2025</text>
								</view>
								<image class="bookmark-icon" src="/static/figma-icons/bookmark-icon.svg" mode="aspectFit"></image>
							</view>
							<view class="result-title-section">
								<text class="result-title">Master of Informatics</text>
							</view>
							<view class="result-author">
								<image class="author-avatar" src="/static/figma-icons/author-avatar.png" mode="aspectFit"></image>
								<text class="author-name">Murad Azimzade</text>
							</view>
							<view class="result-description">
								<text class="more-text">More</text>
								<text class="description-text">There are 4 tests per section
The number of questions is 60
The test duration is 3 hours.</text>
							</view>
						</view>
						<view class="start-button" @click.stop="startTest('master-informatics-3')">
							<text class="start-text">Start</text>
						</view>
					</view>

					<view class="result-card" @click="goToTest('master-informatics-4')">
						<view class="result-content">
							<view class="result-header">
								<view class="result-meta">
									<image class="calendar-icon" src="/static/figma-icons/calendar-icon.svg" mode="aspectFit"></image>
									<text class="result-date">16.04.2025</text>
								</view>
								<image class="bookmark-icon" src="/static/figma-icons/bookmark-icon.svg" mode="aspectFit"></image>
							</view>
							<view class="result-title-section">
								<text class="result-title">Master of Informatics</text>
							</view>
							<view class="result-author">
								<image class="author-avatar" src="/static/figma-icons/author-avatar.png" mode="aspectFit"></image>
								<text class="author-name">Murad Azimzade</text>
							</view>
							<view class="result-description">
								<text class="more-text">More</text>
								<text class="description-text">There are 4 tests per section
The number of questions is 60
The test duration is 3 hours.</text>
							</view>
						</view>
						<view class="start-button" @click.stop="startTest('master-informatics-4')">
							<text class="start-text">Start</text>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 底部导航栏 -->
		<view class="tabbar-container">
			<TabBar currentPage="home" />
		</view>
	</view>
</template>

<script>
import TabBar from '@/components/TabBar.vue'

export default {
	components: {
		TabBar
	},
	data() {
		return {
			searchKeyword: ''
		}
	},
	onLoad() {

	},
	methods: {
		goToCategory(category) {
			console.log('前往分类:', category);
			// 这里可以添加跳转到具体分类页面的逻辑
		},
		goToTest(testId) {
			console.log('前往测试详情:', testId);
			// 这里可以添加跳转到测试详情页面的逻辑
		},
		startTest(testId) {
			console.log('开始测试:', testId);
			// 这里可以添加开始测试的逻辑
		}
	}
}
</script>

<style scoped>
.search-container {
	width: 100%;
	min-height: 100vh;
	background-color: #F2282D;
	display: flex;
	flex-direction: column;
	overflow-x: hidden;
}

/* 欢迎区域 */
.welcome-section {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	padding: 88rpx 34rpx 44rpx;
	background-color: #F2282D;
}

.menu-icon {
	width: 48rpx;
	height: 48rpx;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	gap: 6rpx;
}

.menu-line {
	width: 36rpx;
	height: 4rpx;
}

.welcome-text {
	flex: 1;
	margin: 0 30rpx;
}

.greeting {
	font-family: 'Microsoft YaHei', sans-serif;
	font-size: 40rpx;
	font-weight: 700;
	color: #FFFFFF;
	display: block;
	line-height: 1.3;
	margin-bottom: 14rpx;
}

.subtitle {
	font-family: 'Samsung Sharp Sans', sans-serif;
	font-size: 30rpx;
	font-weight: 400;
	color: #FFFFFF;
	line-height: 1.3;
}

.search-icon {
	width: 84rpx;
	height: 84rpx;
	background-color: #FFFFFF;
	border-radius: 42rpx;
	display: flex;
	justify-content: center;
	align-items: center;
}

.search-img {
	width: 28rpx;
	height: 36rpx;
}

/* 主要内容区域 */
.main-content {
	flex: 1;
	background-color: #FFFFFF;
	border-radius: 40rpx 40rpx 0 0;
	padding: 44rpx 34rpx 34rpx;
	position: relative;
}

/* 搜索栏 */
.search-bar {
	display: flex;
	justify-content: space-between;
	align-items: center;
	gap: 18rpx;
	margin-bottom: 44rpx;
}

.search-input {
	flex: 1;
	height: 84rpx;
	background-color: #EDEDED;
	border-radius: 100rpx;
	border: none;
	display: flex;
	align-items: center;
	padding: 0 52rpx;
	gap: 26rpx;
	box-sizing: border-box;
}

.search-icon-small {
	width: 32rpx;
	height: 32rpx;
}

.search-input-field {
	flex: 1;
	font-family: 'Samsung Sharp Sans', sans-serif;
	font-size: 28rpx;
	font-weight: 500;
	color: #333333;
	border: none;
	outline: none;
}

.filter-icon {
	width: 84rpx;
	height: 84rpx;
	background-color: #EDEDED;
	border-radius: 100rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	box-sizing: border-box;
}

.filter-img {
	width: 36rpx;
	height: 34rpx;
}

/* Live Chat 组件 */
.live-chat-section {
	margin-bottom: 32rpx;
}

.chat-card {
	width: 100%;
	height: 248rpx;
	background: linear-gradient(180deg, #D50006 0%, #D43B3F 100%);
	border-radius: 30rpx;
	position: relative;
	padding: 34rpx 40rpx;
	box-sizing: border-box;
}

.chat-progress {
	position: absolute;
	top: 194rpx;
	right: 20rpx;
	width: 72rpx;
	height: 32rpx;
	background-color: rgba(0, 0, 0, 0.59);
	border-radius: 4rpx;
	display: flex;
	justify-content: center;
	align-items: center;
}

.progress-text {
	font-family: 'Samsung Sharp Sans', sans-serif;
	font-size: 24rpx;
	font-weight: 500;
	color: #FFFFFF;
}

.chat-content {
	width: 478rpx;
}

.chat-title {
	font-family: 'Samsung Sharp Sans', sans-serif;
	font-size: 36rpx;
	font-weight: 700;
	color: #FFFFFF;
	display: block;
	margin-bottom: 18rpx;
	line-height: 1.11;
}

.chat-description {
	font-family: 'Samsung Sharp Sans', sans-serif;
	font-size: 24rpx;
	font-weight: 500;
	color: #FFFFFF;
	line-height: 1.67;
}

.chat-image {
	position: absolute;
	top: 22rpx;
	right: 20rpx;
	width: 176rpx;
	height: 160rpx;
}

/* Categories 标题 */
.categories-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 32rpx;
}

.categories-title {
	font-family: 'Samsung Sharp Sans', sans-serif;
	font-size: 32rpx;
	font-weight: 700;
	color: #000000;
}

.categories-filter {
	width: 64rpx;
	height: 38rpx;
	background-color: rgba(242, 40, 45, 0.14);
	border-radius: 20rpx;
	display: flex;
	justify-content: center;
	align-items: center;
}

.filter-text {
	font-family: 'Samsung Sharp Sans', sans-serif;
	font-size: 20rpx;
	font-weight: 500;
	color: #000000;
}

/* 分类卡片 */
.categories-grid {
	margin-bottom: 32rpx;
}

.category-row {
	display: flex;
	justify-content: flex-start;
	gap: 30rpx;
	margin-bottom: 30rpx;
}

.category-card {
	flex: 1;
	height: 148rpx;
	background-color: #FFFFFF;
	border: 2rpx solid #BFBFBF;
	border-radius: 20rpx;
	padding: 32rpx;
	box-sizing: border-box;
	position: relative;
}

.card-content {
	width: 100%;
	height: 100%;
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
}

.card-header {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.card-title {
	font-family: 'Samsung Sharp Sans', sans-serif;
	font-size: 28rpx;
	font-weight: 700;
	color: #242730;
	letter-spacing: -3%;
}

.see-more {
	font-family: 'Samsung Sharp Sans', sans-serif;
	font-size: 24rpx;
	font-weight: 500;
	color: #A7A7A7;
}

.card-icon {
	width: 96rpx;
	height: 96rpx;
	background: linear-gradient(180deg, #F2282D 0%);
	border-radius: 20rpx;
	display: flex;
	justify-content: center;
	align-items: center;
}

.category-icon {
	width: 48rpx;
	height: 48rpx;
}

/* Featured卡片样式 */
.category-card.featured {
	background-color: #FFFFFF;
	border: 2rpx solid #BFBFBF;
}

.category-card.featured .card-content {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.card-left {
	display: flex;
	align-items: center;
	gap: 24rpx;
}

.card-icon-container {
	width: 64rpx;
	height: 64rpx;
	background-color: #F2282D;
	border-radius: 16rpx;
	display: flex;
	justify-content: center;
	align-items: center;
}

.category-card.featured .category-icon {
	width: 32rpx;
	height: 32rpx;
	filter: brightness(0) invert(1);
}

.card-text {
	display: flex;
	flex-direction: column;
	gap: 4rpx;
}

.arrow-icon {
	width: 32rpx;
	height: 32rpx;
	display: flex;
	justify-content: center;
	align-items: center;
}

.arrow-img {
	width: 16rpx;
	height: 16rpx;
}

/* 搜索结果卡片列表 */
.search-results-section {
	margin-bottom: 32rpx;
}

.result-row {
	display: flex;
	justify-content: flex-start;
	gap: 30rpx;
	margin-bottom: 30rpx;
}

.result-card {
	flex: 1;
	background-color: #FFFFFF;
	border: 2rpx solid #BFBFBF;
	border-radius: 30rpx;
	padding: 32rpx;
	box-sizing: border-box;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}

.result-content {
	display: flex;
	flex-direction: column;
	flex: 1;
}

.result-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 12rpx;
}

.result-title-section {
	margin-bottom: 12rpx;
}

.result-title {
	font-family: 'Samsung Sharp Sans', sans-serif;
	font-size: 20rpx;
	font-weight: 700;
	color: #000000;
	letter-spacing: -3%;
}

.bookmark-icon {
	width: 32rpx;
	height: 32rpx;
}

.result-meta {
	display: flex;
	align-items: center;
	gap: 10rpx;
}

.calendar-icon {
	width: 18rpx;
	height: 18rpx;
}

.result-date {
	font-family: 'Samsung Sharp Sans', sans-serif;
	font-size: 20rpx;
	font-weight: 500;
	color: #8C8C8C;
	letter-spacing: -3%;
}

.result-author {
	display: flex;
	align-items: center;
	gap: 14rpx;
	margin-bottom: 16rpx;
}

.author-avatar {
	width: 48rpx;
	height: 48rpx;
	border-radius: 50%;
	border: 1rpx solid #F2282D;
}

.author-name {
	font-family: 'Samsung Sharp Sans', sans-serif;
	font-size: 20rpx;
	font-weight: 500;
	color: #000000;
	letter-spacing: -4%;
}

.result-description {
	margin-bottom: 24rpx;
	margin-left: 4rpx;
}

.more-text {
	font-family: 'Samsung Sharp Sans', sans-serif;
	font-size: 16rpx;
	font-weight: 700;
	color: #000000;
	letter-spacing: -3%;
	display: block;
	margin-bottom: 0;
}

.description-text {
	font-family: 'Samsung Sharp Sans', sans-serif;
	font-size: 16rpx;
	font-weight: 500;
	color: #8C8C8C;
	line-height: 1.875;
	letter-spacing: -3%;
}

.start-button {
	width: 136rpx;
	height: 42rpx;
	background-color: #F2282D;
	border-radius: 2rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	margin-left: 66rpx;
}

.start-text {
	font-family: 'Samsung Sharp Sans', sans-serif;
	font-size: 24rpx;
	font-weight: 700;
	color: #FFFFFF;
}

/* TabBar容器 */
.tabbar-container {
	background-color: #FFFFFF;
	padding: 0 34rpx 34rpx;
	display: flex;
	justify-content: center;
}

/* uniapp使用rpx单位自动适配不同屏幕尺寸，无需媒体查询 */
</style>
